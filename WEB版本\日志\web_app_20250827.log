2025-08-27 08:56:54,644 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:54,649 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:56:56,547 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-27 08:56:59,316 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:56:59,322 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:43,817 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 08:57:43,817 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 08:57:43,818 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 08:57:45,129 - WARNING -  * <PERSON>bu<PERSON> is active!
2025-08-27 08:57:45,135 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 08:57:46,869 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:46] "GET / HTTP/1.1" 200 -
2025-08-27 08:57:47,063 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,069 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:47,683 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:47,684 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-sK HTTP/1.1" 200 -
2025-08-27 08:57:47,685 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 08:57:47,742 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "POST /socket.io/?EIO=4&transport=polling&t=PZf1-tN&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,744 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-tO&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,796 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uI&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:47,811 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf1-uT&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:48,896 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:48,941 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:48,943 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:48] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:49,075 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CD HTTP/1.1" 200 -
2025-08-27 08:57:49,076 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_CE HTTP/1.1" 200 -
2025-08-27 08:57:49,079 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DE&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,140 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_DF.0&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,141 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=websocket&sid=kLM6pA5CnkhYhYwqAAAA HTTP/1.1" 200 -
2025-08-27 08:57:49,142 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DF.1&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,152 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DT&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,157 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_DV&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:49,175 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Dq&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:49,180 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:49] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ds&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,311 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=E5SFlIU1U4jGBBV8AAAC HTTP/1.1" 200 -
2025-08-27 08:57:50,318 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=websocket&sid=f8rO8BigQSxw0NyTAAAD HTTP/1.1" 200 -
2025-08-27 08:57:50,344 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,347 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,467 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xy HTTP/1.1" 200 -
2025-08-27 08:57:50,468 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 08:57:50,475 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Xz HTTP/1.1" 200 -
2025-08-27 08:57:50,504 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_YZ&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,505 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Ya&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_Yb&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,513 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Yb.0&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,529 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_Y_&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,541 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_ZA&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:50,747 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 08:57:50,797 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 08:57:50,798 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:50] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 08:57:51,027 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_gk HTTP/1.1" 200 -
2025-08-27 08:57:51,054 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 08:57:51,064 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf1_hL&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,065 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=8QNxA4DsC9LM3G4aAAAH HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_hL.0&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 08:57:51,066 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=websocket&sid=c5O2B3Fg6ld0sXKFAAAG HTTP/1.1" 200 -
2025-08-27 08:57:51,124 - INFO - 127.0.0.1 - - [27/Aug/2025 08:57:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf1_iF&sid=KBfu-E8SkQguB168AAAK HTTP/1.1" 200 -
2025-08-27 09:02:00,468 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:00,469 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\主程序.py', reloading
2025-08-27 09:02:01,465 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:02,637 - WARNING -  * Debugger is active!
2025-08-27 09:02:02,640 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:03,222 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFJ HTTP/1.1" 200 -
2025-08-27 09:02:03,227 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "POST /socket.io/?EIO=4&transport=polling&t=PZf2zFO&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:03,228 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:03] "GET /socket.io/?EIO=4&transport=polling&t=PZf2zFO.0&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:02:22,701 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:02:22,701 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:02:22,702 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:02:23,888 - WARNING -  * Debugger is active!
2025-08-27 09:02:23,893 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:02:50,874 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:50] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:02:51,039 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,053 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:02:51,579 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf392v HTTP/1.1" 200 -
2025-08-27 09:02:51,582 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:02:51,619 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf393V&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,620 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf393W&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:51,888 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf3971&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:02:52,666 - INFO - 127.0.0.1 - - [27/Aug/2025 09:02:52] "GET /socket.io/?EIO=4&transport=websocket&sid=BCWHcSZOUUOhuzKtAAAA HTTP/1.1" 200 -
2025-08-27 09:04:09,807 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:09,836 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:09,848 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:09] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:10,433 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SIz HTTP/1.1" 200 -
2025-08-27 09:04:10,439 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:04:10,488 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "POST /socket.io/?EIO=4&transport=polling&t=PZf3SJr&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,489 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SJr.0&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:10,766 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:10] "GET /socket.io/?EIO=4&transport=polling&t=PZf3SNE&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:23,620 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:04:23,620 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:04:23,620 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:04:24,912 - WARNING -  * Debugger is active!
2025-08-27 09:04:24,916 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:04:37,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:04:37,279 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:37,285 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:37] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:38,022 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:04:38,029 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z1B HTTP/1.1" 200 -
2025-08-27 09:04:38,119 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "POST /socket.io/?EIO=4&transport=polling&t=PZf3Z39&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:38,120 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z3A&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:38,456 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:38] "GET /socket.io/?EIO=4&transport=polling&t=PZf3Z7d&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:44,648 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:44] "GET /socket.io/?EIO=4&transport=websocket&sid=rWVvJp8KIvJhvriJAAAC HTTP/1.1" 200 -
2025-08-27 09:04:44,899 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:44] "GET /socket.io/?EIO=4&transport=websocket&sid=-G-kczaCy84mOUUkAAAE HTTP/1.1" 200 -
2025-08-27 09:04:46,957 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:46] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-27 09:04:47,016 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:04:47,017 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:04:47,149 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bGf HTTP/1.1" 200 -
2025-08-27 09:04:47,152 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:04:47,193 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "POST /socket.io/?EIO=4&transport=polling&t=PZf3bHL&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,195 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bHL.0&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=websocket&sid=GLWSgecFYeYlB-Y3AAAG HTTP/1.1" 200 -
2025-08-27 09:04:47,226 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bHr&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:04:47,245 - INFO - 127.0.0.1 - - [27/Aug/2025 09:04:47] "GET /socket.io/?EIO=4&transport=polling&t=PZf3bI3&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:05:11,352 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:05:11,389 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:11,389 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:11,565 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hE7 HTTP/1.1" 200 -
2025-08-27 09:05:11,566 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:05:11,608 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "POST /socket.io/?EIO=4&transport=polling&t=PZf3hEr&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,610 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hEs&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,613 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=websocket&sid=-2Lkz0ktHoWKHpOrAAAI HTTP/1.1" 200 -
2025-08-27 09:05:11,627 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hF6&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:11,698 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:11] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hGF&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:12,063 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /系统设置 HTTP/1.1" 200 -
2025-08-27 09:05:12,136 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:12,138 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:12,297 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hPV HTTP/1.1" 200 -
2025-08-27 09:05:12,347 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "POST /socket.io/?EIO=4&transport=polling&t=PZf3hQO&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:12,349 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=websocket&sid=Vj4Hk_mQLvjrPd-OAAAK HTTP/1.1" 200 -
2025-08-27 09:05:12,349 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hQO.0&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:12,364 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:12] "GET /socket.io/?EIO=4&transport=polling&t=PZf3hQe&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:33,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:05:33,113 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,118 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,342 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYQ HTTP/1.1" 200 -
2025-08-27 09:05:33,343 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYR HTTP/1.1" 200 -
2025-08-27 09:05:33,345 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:33,375 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mYv&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,377 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=websocket&sid=Otnx8p1vOkObCcliAAAM HTTP/1.1" 200 -
2025-08-27 09:05:33,378 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYw&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,380 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mYw.0&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,381 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mYx&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,410 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mZO&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,411 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mZP&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,447 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ma4&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:33,447 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ma5&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:33,930 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "GET / HTTP/1.1" 200 -
2025-08-27 09:05:33,956 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:33,959 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:33] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:34,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=websocket&sid=5R-eW8pGkKWUQfaJAAAO HTTP/1.1" 200 -
2025-08-27 09:05:34,075 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=websocket&sid=YHEkDJNKVvcX2gEzAAAP HTTP/1.1" 200 -
2025-08-27 09:05:34,085 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mjr HTTP/1.1" 200 -
2025-08-27 09:05:34,087 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:05:34,088 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:34,135 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "POST /socket.io/?EIO=4&transport=polling&t=PZf3mkj&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:34,137 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3mkk&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:34,153 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:34] "GET /socket.io/?EIO=4&transport=polling&t=PZf3ml7&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:48,193 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /数据采集 HTTP/1.1" 200 -
2025-08-27 09:05:48,232 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:05:48,233 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:05:48,382 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qDE HTTP/1.1" 200 -
2025-08-27 09:05:48,387 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qDF HTTP/1.1" 200 -
2025-08-27 09:05:48,388 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:05:48,394 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=websocket&sid=mUeG22LsSrH_Lek1AAAS HTTP/1.1" 200 -
2025-08-27 09:05:48,444 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "POST /socket.io/?EIO=4&transport=polling&t=PZf3qEJ&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,445 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEK&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,446 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "POST /socket.io/?EIO=4&transport=polling&t=PZf3qEL&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,446 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEM&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,473 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEr&sid=sDYJB_ax1O_dK213AAAU HTTP/1.1" 200 -
2025-08-27 09:05:48,474 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qEs&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:05:48,512 - INFO - 127.0.0.1 - - [27/Aug/2025 09:05:48] "GET /socket.io/?EIO=4&transport=polling&t=PZf3qFT&sid=m1XilxyBNRkc-SraAAAV HTTP/1.1" 200 -
2025-08-27 09:08:25,186 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:08:25,187 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:08:25,187 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:08:26,695 - WARNING -  * Debugger is active!
2025-08-27 09:08:26,700 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:08:40,236 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:08:40,340 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:08:40,357 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-27 09:08:40,820 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UJn HTTP/1.1" 200 -
2025-08-27 09:08:40,821 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:08:40,862 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "POST /socket.io/?EIO=4&transport=polling&t=PZf4UKQ&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:40,865 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:40] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UKR&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:41,118 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:41] "GET /socket.io/?EIO=4&transport=polling&t=PZf4UNl&sid=iwKzftLvuhOxSQNnAAAA HTTP/1.1" 200 -
2025-08-27 09:08:51,184 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-27 09:08:51,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:08:51,223 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:08:51,749 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4W-Z HTTP/1.1" 200 -
2025-08-27 09:08:51,751 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /api/获取新上架商品 HTTP/1.1" 200 -
2025-08-27 09:08:51,796 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "POST /socket.io/?EIO=4&transport=polling&t=PZf4W_F&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:08:51,797 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4W_G&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:08:51,971 - INFO - 127.0.0.1 - - [27/Aug/2025 09:08:51] "GET /socket.io/?EIO=4&transport=polling&t=PZf4X21&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:09:14,467 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-27 09:09:14,619 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:09:14,647 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:09:14,783 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:09:14,787 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=websocket&sid=24EQ_vMxSC3p36f1AAAC HTTP/1.1" 200 -
2025-08-27 09:09:14,792 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=polling&t=PZf4cbD HTTP/1.1" 200 -
2025-08-27 09:09:14,821 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "POST /socket.io/?EIO=4&transport=polling&t=PZf4ccz&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:14,888 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /socket.io/?EIO=4&transport=polling&t=PZf4cc-&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:14,905 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "GET /数据查看 HTTP/1.1" 200 -
2025-08-27 09:09:14,994 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:14] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-27 09:09:15,011 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-27 09:09:15,144 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4ci0 HTTP/1.1" 200 -
2025-08-27 09:09:15,148 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /api/获取商品数据 HTTP/1.1" 200 -
2025-08-27 09:09:15,156 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=websocket&sid=yoRXqs6X4ZeOYeGZAAAE HTTP/1.1" 200 -
2025-08-27 09:09:15,177 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "POST /socket.io/?EIO=4&transport=polling&t=PZf4ciR&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:09:15,197 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4ciS&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:09:15,479 - INFO - 127.0.0.1 - - [27/Aug/2025 09:09:15] "GET /socket.io/?EIO=4&transport=polling&t=PZf4clr&sid=JAYyQGip9FFt0JiOAAAG HTTP/1.1" 200 -
2025-08-27 09:29:52,807 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:29:52,808 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:29:52,808 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:29:53,547 - WARNING -  * Debugger is active!
2025-08-27 09:29:53,551 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:29:53,561 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9KjN HTTP/1.1" 200 -
2025-08-27 09:29:53,562 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9KjN HTTP/1.1" 200 -
2025-08-27 09:29:53,568 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "POST /socket.io/?EIO=4&transport=polling&t=PZf9L2R&sid=M7Uozy6axcJKHj9hAAAA HTTP/1.1" 200 -
2025-08-27 09:29:53,570 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "POST /socket.io/?EIO=4&transport=polling&t=PZf9L2U&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:29:53,571 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2T&sid=M7Uozy6axcJKHj9hAAAA HTTP/1.1" 200 -
2025-08-27 09:29:53,572 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2V&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:29:53,594 - INFO - 127.0.0.1 - - [27/Aug/2025 09:29:53] "GET /socket.io/?EIO=4&transport=polling&t=PZf9L2c&sid=QjoASvykpBbNnU-CAAAB HTTP/1.1" 200 -
2025-08-27 09:31:40,332 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:31:40,333 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:31:40,333 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:31:40,949 - WARNING -  * Debugger is active!
2025-08-27 09:31:40,953 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:31:45,210 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIu HTTP/1.1" 200 -
2025-08-27 09:31:45,212 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIu HTTP/1.1" 200 -
2025-08-27 09:31:45,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "POST /socket.io/?EIO=4&transport=polling&t=PZf9mIy&sid=okBkDYasXBGEBZEHAAAA HTTP/1.1" 200 -
2025-08-27 09:31:45,217 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mIz&sid=okBkDYasXBGEBZEHAAAA HTTP/1.1" 200 -
2025-08-27 09:31:45,219 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "POST /socket.io/?EIO=4&transport=polling&t=PZf9mI_&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:31:45,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mJ0&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:31:45,234 - INFO - 127.0.0.1 - - [27/Aug/2025 09:31:45] "GET /socket.io/?EIO=4&transport=polling&t=PZf9mJ6&sid=pBpHkSmFLmW02XB1AAAB HTTP/1.1" 200 -
2025-08-27 09:32:04,700 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:32:04,700 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:32:04,701 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:32:05,342 - WARNING -  * Debugger is active!
2025-08-27 09:32:05,345 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:32:07,230 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rgn HTTP/1.1" 200 -
2025-08-27 09:32:07,241 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "POST /socket.io/?EIO=4&transport=polling&t=PZf9rh2&sid=BHjx5vJS0_V69uebAAAA HTTP/1.1" 200 -
2025-08-27 09:32:07,243 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:07] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rh4&sid=BHjx5vJS0_V69uebAAAA HTTP/1.1" 200 -
2025-08-27 09:32:08,216 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rwM HTTP/1.1" 200 -
2025-08-27 09:32:08,221 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "POST /socket.io/?EIO=4&transport=polling&t=PZf9rwQ&sid=iWvRtEdudGUz7d1NAAAC HTTP/1.1" 200 -
2025-08-27 09:32:08,223 - INFO - 127.0.0.1 - - [27/Aug/2025 09:32:08] "GET /socket.io/?EIO=4&transport=polling&t=PZf9rwR&sid=iWvRtEdudGUz7d1NAAAC HTTP/1.1" 200 -
2025-08-27 09:33:16,827 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-27 09:33:16,828 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 09:33:16,828 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 09:33:17,496 - WARNING -  * Debugger is active!
2025-08-27 09:33:17,500 - INFO -  * Debugger PIN: 423-099-573
2025-08-27 09:33:17,508 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6mX HTTP/1.1" 200 -
2025-08-27 09:33:17,511 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6mX HTTP/1.1" 200 -
2025-08-27 09:33:17,517 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "POST /socket.io/?EIO=4&transport=polling&t=PZfA6r6&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
2025-08-27 09:33:17,517 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6r7&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
2025-08-27 09:33:17,519 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "POST /socket.io/?EIO=4&transport=polling&t=PZfA6rA&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:17,520 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6rC&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:17,532 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:17] "GET /socket.io/?EIO=4&transport=polling&t=PZfA6rI&sid=NjZDR_mly1wUFNzCAAAB HTTP/1.1" 200 -
2025-08-27 09:33:19,900 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET / HTTP/1.1" 200 -
2025-08-27 09:33:19,993 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-27 09:33:19,993 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:19] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-27 09:33:20,455 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7Z5 HTTP/1.1" 200 -
2025-08-27 09:33:20,457 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-27 09:33:20,458 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-27 09:33:20,490 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "POST /socket.io/?EIO=4&transport=polling&t=PZfA7Ze&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,492 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7Zf&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,527 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7aC&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:20,571 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:20] "GET /socket.io/?EIO=4&transport=polling&t=PZfA7au&sid=gTMO1t4ZfJtNN3OFAAAE HTTP/1.1" 200 -
2025-08-27 09:33:22,003 - INFO - 127.0.0.1 - - [27/Aug/2025 09:33:22] "GET /socket.io/?EIO=4&transport=websocket&sid=qN6dwdEW9UEskC94AAAA HTTP/1.1" 200 -
